from utils.meilisearch.indexer import (
    GenericIndexer,
    GenericSessionsIndexer,
)


class CourseModel(GenericSessionsIndexer):
    pass


class CourseSessionModel(GenericIndexer):
    pass


courses = CourseModel(
    index='courses', indexed_id='course', sessions_key='chapters')
course_sessions = CourseSessionModel(
    index='lessons', indexed_id='course-session')
get_sessions_ids = CourseModel.get_sessions_ids_from_object
