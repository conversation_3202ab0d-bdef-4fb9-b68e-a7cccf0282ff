def wrap_content_type_filter(basename, query_params={}):
    """
    Wraps the content type filter in a list if it's not already wrapped.

    :param query_params: query params from the request
    :param basename: basename of the viewset
    :return: query params with the content type filter wrapped in a list
    """
    basename_filter_mapping = {
        'clubs': 'recurring = True',
        'audio-journeys': 'recurring = False',
        'q-and-a': 'q-and-a = True',
        'clubs-series_sessions': 'contentType = "clubs"',
        'audio-journeys-series_sessions': 'contentType = "audio-journeys"',
    }
    content_type_filter_query = basename_filter_mapping.get(basename, '')
    if not content_type_filter_query:
        return query_params

    query_params['filter'] = content_type_filter_query if \
        not query_params.get('filter', False) else \
        f'({query_params.get("filter")}) AND {content_type_filter_query}'

    return query_params
