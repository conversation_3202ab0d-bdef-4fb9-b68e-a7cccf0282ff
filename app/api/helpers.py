from api.courses.models import (
    courses,
    course_sessions,
)
from api.events.models import events


def wrap_content_type_filter(basename, query_params={}):
    """
    Wraps the content type filter in a list if it's not already wrapped.

    :param query_params: query params from the request
    :param basename: basename of the viewset
    :return: query params with the content type filter wrapped in a list
    """
    basename_filter_mapping = {
        'clubs': 'recurring = True',
        'audio-journeys': 'recurring = False',
        'q-and-a': 'q-and-a = True',
        'clubs-series_sessions': 'contentType = "clubs"',
        'audio-journeys-series_sessions': 'contentType = "audio-journeys"',
    }
    content_type_filter_query = basename_filter_mapping.get(basename, '')
    if not content_type_filter_query:
        return query_params

    query_params['filter'] = content_type_filter_query if \
        not query_params.get('filter', False) else \
        f'({query_params.get("filter")}) AND {content_type_filter_query}'

    return query_params


def wrap_discourse_filter(thread_id, query_params={}):
    """
    Adds a discourse/thread ID filter to the query parameters.

    :param thread_id: UUID of the discourse thread
    :param query_params: query params from the request
    :return: query params with the discourse filter added
    """
    if not thread_id:
        return query_params

    discourse_filter_query = f'discourseId = "{thread_id}"'

    query_params['filter'] = discourse_filter_query if \
        not query_params.get('filter', False) else \
        f'({query_params.get("filter")}) AND {discourse_filter_query}'

    return query_params


def filter_content_by_discourse_id(thread_id, query_params={}):
    """
    Filter content from multiple collections by discourse/thread ID.

    :param thread_id: UUID of the discourse thread
    :param query_params: additional query parameters
    :return: dictionary with results from each collection
    """
    if not thread_id:
        return {}

    content = None
    filtered_params = wrap_discourse_filter(thread_id, query_params.copy())

    for collection in [courses, events, course_sessions]:
        try:
            search_results = collection.search(filtered_params)
            if search_results.get('hits') and len(search_results['hits']):
                content = search_results['hits'][0]
                break
        except Exception as e:
            # Log error but continue with other collections
            print(f"Error filtering {collection.get_index_key()} by discourse ID {thread_id}: {e}")

    return content
