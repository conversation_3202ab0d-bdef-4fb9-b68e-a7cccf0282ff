from rest_framework import permissions

# from django.core.exceptions import ObjectDoesNotExist

from config import settings

# from utils.meilisearch.router import (
#     content_type_index_mapper as index_mapper
# )


class IsStaffOrOwnAccount(permissions.BasePermission):
    message = 'You have no permissions to perform that action.'

    def has_permission(self, request, view):
        if request.auth or request.user:
            user = request.user
        else:
            return False

        try:
            if user.is_authenticated:
                if view.kwargs.get('pk') != 'current':
                    return user.is_staff or str(user.pk) == view.kwargs.get('pk')
                else:
                    return True
        except AttributeError:
            return False
        return False


class IsStaff(permissions.BasePermission):
    message = 'You have no permissions to perform that action.'

    def has_permission(self, request, view):
        try:
            return request.user.is_staff
        except Exception:
            return False


# class IsFreePlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         content_type = view.kwargs.get('content_type')
#         slug = view.kwargs.get('pk')
#         view.get_view_name().lower()
#         print(view.get_object())
#         product = index_mapper['courses'].get(slug)
#
#         return product.get('price') == 0
#
#
# class IsThrivePlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             if request.user.plan_id == settings.CHARGEBEE_THRIVE_PLAN_ID:
#                 return True
#         except AttributeError:
#             return False
#         return False
#
#
# class IsCommunityPlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             allowed_content_types = ['films', 'podcasts', 'articles']
#             content_type = view.kwargs.get('content_type')
#             if (user.plan_id == settings.CHARGEBEE_COMMUNITY_PLAN_ID and
#                     content_type in allowed_content_types):
#                 return True
#         except AttributeError:
#             return False
#         return False
#
#
# class IsSinglePurchase(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             # if user has purchased the product outside of plan scope
#             product = user.content.get(pk=view.kwargs.get('pk'))
#
#             return product.on_demand
#         except ObjectDoesNotExist:
#             return False
#         return False


class ReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.method in permissions.SAFE_METHODS


class IsCMSRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        return settings.CMS_URL in view.request.META['HTTP_REFERER']


class IsChargebeeRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        magic_key = request.query_params.get('magicKey')
        return settings.CHARGEBEE_MAGIC_KEY == magic_key


class IsDigitalOceanFunctionsRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        # get "magicKey" param from form POST
        magic_key = request.data.get('magicKey')
        return settings.DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN == magic_key


class IsCMSRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        magic_key = request.query_params.get('magicKey')
        return settings.CMS_MAGIC_KEY == magic_key


def check_product_permissions(user, content_type, instance, plan_ids=None):
    """
    Permission check for Course or Event object.

    Criteria:
    - user is staff
    - the course/event is free
    - the user has an active CHARGEBEE_THRIVE_PLAN_ID membership subscription
    - the user has purchased the product individually in an on-demand order
    - the product is active (meaning the purchase was completed successfully)

    :param user: current user or any given user
    :param content_type: 'courses' or 'events'
    :param instance: the query response from Meilisearch API
    :param plan_ids: user plan_id to check or CHARGEBEE_THRIVE_PLAN_ID ('thrive')
    :return: boolean indicating whether the user is allowed to view the object
    """
    if plan_ids is None:
        plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                    settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS)
        if content_type == 'events':
            plan_ids += (settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                         settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    if ((instance.get('price') == 0 and content_type != 'events') or
            (user.is_authenticated and (
                    user.is_staff or
                    user.plan_id in plan_ids or
                    user.content.filter(
                        content_type=content_type,
                        on_demand=True,
                        slug=instance.get('slug')).exists()))):
        return True
    return False


def check_course_permissions(user, course, content_type='courses'):
    course_plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                       settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS)

    return check_product_permissions(
        user, content_type, course, course_plan_ids)


def check_event_permissions(user, event, content_type='events'):
    event_plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    return check_product_permissions(user, content_type, event, event_plan_ids)


def check_media_permissions(user, media, content_type):
    media_plan_ids = (settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    return check_product_permissions(user, content_type, media, media_plan_ids)
