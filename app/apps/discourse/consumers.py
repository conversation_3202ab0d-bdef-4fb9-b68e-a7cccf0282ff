import json
import uuid
import asyncio

from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.core.serializers.json import DjangoJSONEncoder

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.cache import ChatCacheManager

User = get_user_model()


class UUIDEncoder(DjangoJSONEncoder):
    """
    JSON encoder that handles UUID objects by converting them to strings.
    """
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for individual chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']
            self.thread_id = self.scope['url_route']['kwargs']['thread_id']
            self.room_group_name = f'thread_{self.thread_id}'

            if self.user.is_anonymous:
                await self.close(code=4001)
                return

            is_participant = await self.is_thread_participant()
            if not is_participant:
                await self.close(code=4003)
                return

            # Join room group with error handling
            try:
                await self.channel_layer.group_add(
                    self.room_group_name,
                    self.channel_name
                )
            except Exception as group_error:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"ChatConsumer failed to join group: {group_error}")
                # Continue without group functionality

            await self.accept()

            # Send last 50 messages to the newly connected client
            messages = await self.get_thread_messages()
            if messages:
                await self.send(text_data=json.dumps({
                    'type': 'history',
                    'messages': messages
                }, cls=UUIDEncoder))
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error: {str(e)}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            try:
                await self.channel_layer.group_discard(
                    self.room_group_name,
                    self.channel_name
                )
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"ChatConsumer failed to leave group on disconnect: {e}")

    async def receive(self, text_data):
        """
        Receive message from WebSocket.
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type', 'message')

            if message_type == 'message':
                # Create a new message or reply
                message_body = data.get('body', '')
                parent_message_id = data.get('parent_message')
                attachment_url = data.get('attachment_url')

                message = await self.create_message(message_body, parent_message_id, attachment_url)

                # Send message to room group
                try:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'chat_message',
                            'message': message
                        }
                    )
                except Exception as group_error:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Failed to send message to group: {group_error}")
                    # Send message directly to this connection
                    await self.send(text_data=json.dumps({
                        'type': 'message',
                        'message': message
                    }, cls=UUIDEncoder))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }, cls=UUIDEncoder))

    async def chat_message(self, event):
        """
        Receive message from room group and send to WebSocket.
        """
        message = event['message']

        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': message
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def is_thread_participant(self):
        """
        Check if the user is a participant in this thread.
        For discussions: public discussions are accessible to all authenticated users.
        For chats: only participants or public chats are accessible.
        """
        try:
            thread = ChatThread.objects.get(id=self.thread_id)

            if thread.discourse_type == 'discussions':
                # For discussions: allow access to public discussions or if user is participant
                return not thread.is_private or thread.participants.filter(id=self.user.id).exists()
            else:
                # For chats: allow access to participants or public chats
                return thread.participants.filter(id=self.user.id).exists() or not thread.is_private
        except ChatThread.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, body, parent_message_id=None, attachment_url=None):
        """
        Create a new message or reply in the database.
        """
        thread = ChatThread.objects.get(id=self.thread_id)

        # Handle parent message for replies
        parent_message = None
        if parent_message_id:
            try:
                parent_message = ChatMessage.objects.get(id=parent_message_id, thread=thread)
                # Ensure only 1 level of nesting - if replying to a reply, reply to the root message
                if parent_message.parent_message is not None:
                    parent_message = parent_message.parent_message
            except ChatMessage.DoesNotExist:
                # If parent message doesn't exist, create as regular message
                parent_message = None

        message = ChatMessage.objects.create(
            thread=thread,
            author=self.user,
            body=body,
            parent_message=parent_message,
            attachment_url=attachment_url
        )
        # Cache the message
        return ChatCacheManager.cache_message(message)

    @database_sync_to_async
    def get_thread_messages(self):
        """
        Get the last 50 root messages for this thread (replies are nested in serializer).
        """
        # Try to get from cache first
        cached_messages = ChatCacheManager.get_thread_messages(self.thread_id)
        if cached_messages is not None:
            return cached_messages

        # If not in cache, get from database - only root messages
        messages = ChatMessage.objects.filter(
            thread_id=self.thread_id,
            parent_message__isnull=True
        ).order_by('-created')[:50]
        if messages:
            # Cache and return the messages
            return ChatCacheManager.cache_thread_messages(self.thread_id, messages)
        return []


class ChatListConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for receiving updates about chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']
            import logging
            logger = logging.getLogger(__name__)

            # Log connection attempt
            logger.info(f"ChatListConsumer connection attempt - User: {self.user}, Anonymous: {self.user.is_anonymous}")
            logger.info(f"ChatListConsumer scope keys: {list(self.scope.keys())}")
            logger.info(f"ChatListConsumer path: {self.scope.get('path')}")
            logger.info(f"ChatListConsumer query_string: {self.scope.get('query_string', b'').decode()}")

            # Check if user is authenticated
            if self.user.is_anonymous:
                logger.warning("ChatListConsumer connection rejected - User not authenticated")
                await self.close(code=4001)
                return

            self.room_group_name = f'user_{self.user.id}_threads'
            logger.info(f"ChatListConsumer connection accepted for user {self.user.id}")

            # Join room group with retry logic
            max_retries = 3
            group_add_success = False
            for attempt in range(max_retries):
                try:
                    await self.channel_layer.group_add(
                        self.room_group_name,
                        self.channel_name
                    )
                    group_add_success = True
                    break
                except Exception as group_error:
                    logger.warning(f"Group add attempt {attempt + 1} failed: {group_error}")
                    if attempt == max_retries - 1:
                        logger.error(f"Failed to join group after {max_retries} attempts. Continuing without group functionality.")
                        # Don't raise the error, continue without group functionality
                        break
                    await asyncio.sleep(1)  # Wait before retry

            if not group_add_success:
                logger.warning(f"ChatListConsumer proceeding without Redis group functionality for user {self.user.id}")

            await self.accept()

            # Send current threads to the newly connected client
            threads = await self.get_user_threads()
            await self.send(text_data=json.dumps({
                'type': 'thread_list',
                'threads': threads
            }, cls=UUIDEncoder))

            logger.info(f"WebSocket connection fully established for user {self.user.id}")
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error in ChatListConsumer: {str(e)}")
            import traceback
            logger.error(f"WebSocket error traceback: {traceback.format_exc()}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            try:
                await self.channel_layer.group_discard(
                    self.room_group_name,
                    self.channel_name
                )
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to leave group on disconnect: {e}")

    async def thread_update(self, event):
        """
        Receive thread update from room group and send to WebSocket.
        """
        thread = event['thread']

        # Send thread update to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'thread_update',
            'thread': thread
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def get_user_threads(self):
        """
        Get all threads for this user based on the WebSocket path.
        """
        path = self.scope.get('path', '')

        if '/ws/discussions/' in path:
            # For discussions WebSocket, get discussions with participant threads first
            from django.db.models import Q, Case, When, Value, IntegerField
            threads = ChatThread.objects.filter(
                discourse_type='discussions'
            ).filter(
                Q(is_private=False) | Q(participants=self.user)
            ).annotate(
                # Add ordering priority: 0 for participant threads, 1 for public non-participant threads
                priority=Case(
                    When(participants=self.user, then=Value(0)),
                    default=Value(1),
                    output_field=IntegerField()
                )
            ).distinct().order_by('priority', '-created')
        else:
            # For chat WebSocket, get chats
            threads = ChatThread.objects.filter(
                discourse_type='chats',
                participants=self.user
            )

        return ChatThreadSerializer(threads, many=True).data

